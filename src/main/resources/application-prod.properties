# Production configuration
server.port=${PORT:8090}

# Logging configuration for production
logging.level.org.example=INFO
logging.level.org.springframework.security=WARN

# Actuator configuration for production
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when-authorized

# Security for production
spring.security.user.name=${ADMIN_USERNAME:developer}
spring.security.user.password=${ADMIN_PASSWORD:test123}
