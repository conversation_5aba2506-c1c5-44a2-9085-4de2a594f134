# Application configuration
server.port=8092

# Application name
spring.application.name=tracking-number-generator

# Logging configuration
logging.level.org.example=INFO
logging.level.org.springframework.security=WARN

# Actuator configuration
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=always

# Security configuration
spring.security.user.name=${ADMIN_USERNAME:developer}
spring.security.user.password=${ADMIN_PASSWORD:test123}