# Application configuration
server.port=8092

# Application name
spring.application.name=tracking-number-generator

# Logging configuration
logging.level.org.example=INFO
logging.level.org.springframework.security=WARN
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{correlationId:-}] %logger{36} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{correlationId:-}] %logger{36} - %msg%n

# Actuator configuration
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=always
management.metrics.export.prometheus.enabled=true

# Security configuration
spring.security.user.name=${ADMIN_USERNAME:developer}
spring.security.user.password=${ADMIN_PASSWORD:test123}